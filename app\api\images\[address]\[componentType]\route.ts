import { NextRequest } from 'next/server';

interface Context {
  params: Promise<{ address: string; componentType: string }>;
}

export async function GET(
  _request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { address, componentType } = await context.params;

    if (!address || !componentType) {
      return Response.json(
        { error: 'Address and component type are required' },
        { status: 400 }
      );
    }

    // During build time, return empty array
    if (process.env.NEXT_PHASE === 'phase-production-build') {
      return Response.json([]);
    }

    // Import database modules only when needed
    const { db } = await import('@/db/drizzle');
    const { componentImages } = await import('@/db/schema');
    const { eq, and } = await import('drizzle-orm');

    // Fetch images for the specified address and component type
    const images = await db
      .select()
      .from(componentImages)
      .where(
        and(
          eq(componentImages.address, address),
          eq(componentImages.componentType, componentType)
        )
      );

    return Response.json(images);
  } catch (error: any) {
    console.error('Failed to fetch images:', error);

    if (error.code === 'ECONNREFUSED') {
      return Response.json(
        { error: 'Database is currently unavailable. Please try again later.' },
        { status: 503 }
      );
    }

    return Response.json(
      { error: 'Failed to fetch images' },
      { status: 500 }
    );
  }
}
