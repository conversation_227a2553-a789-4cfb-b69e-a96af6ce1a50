import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { componentImages } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

interface Context {
  params: Promise<{ address: string; componentType: string }>;
}

export async function GET(
  _request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { address, componentType } = await context.params;

    if (!address || !componentType) {
      return Response.json(
        { error: 'Address and component type are required' },
        { status: 400 }
      );
    }

    // Fetch images for the specified address and component type
    const images = await db
      .select()
      .from(componentImages)
      .where(
        and(
          eq(componentImages.address, address),
          eq(componentImages.componentType, componentType)
        )
      );

    return Response.json(images);
  } catch (error: any) {
    console.error('Failed to fetch images:', error);

    if (error.code === 'ECONNREFUSED') {
      return Response.json(
        { error: 'Database is currently unavailable. Please try again later.' },
        { status: 503 }
      );
    }

    return Response.json(
      { error: 'Failed to fetch images' },
      { status: 500 }
    );
  }
}
