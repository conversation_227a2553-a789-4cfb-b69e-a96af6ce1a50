import { config } from "dotenv";
import { drizzle } from 'drizzle-orm/mysql2';
import mysql from 'mysql2/promise';
import * as schema from './schema';

config({ path: ".env" }); // or .env.local

// Parse the database connection string
const connectionString = process.env.DATABASE_URL!;

if (!connectionString) {
  throw new Error('DATABASE_URL environment variable is required');
}

// Parse connection string manually to avoid sslmode warning
const regex = /mysql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/;
const match = connectionString.match(regex);

if (!match) {
  throw new Error(`Invalid connection string format: ${connectionString}`);
}

const [, user, password, host, port, database] = match;

// Create a MySQL connection pool
const pool = mysql.createPool({
  host,
  port: parseInt(port),
  user,
  password,
  database,
  ssl: process.env.MYSQL_SSL === 'true' ? { rejectUnauthorized: false } : undefined,
  connectionLimit: 10,
  waitForConnections: true,
  queueLimit: 0
});

// Create a drizzle client with the MySQL connection
export const db = drizzle(pool, { schema, mode: 'default' });
