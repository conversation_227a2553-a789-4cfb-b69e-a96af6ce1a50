'use client';

import React, { useState, useEffect, useRef } from 'react';

interface DecryptedTextProps {
  text: string;
  speed?: number;
  maxIterations?: number;
  sequential?: boolean;
  revealDirection?: 'start' | 'end' | 'center' | 'random';
  animateOn?: 'mount' | 'view' | 'manual';
  className?: string;
  style?: React.CSSProperties;
  onComplete?: () => void;
}

const DecryptedText: React.FC<DecryptedTextProps> = ({
  text,
  speed = 50,
  maxIterations = 10,
  sequential = false,
  revealDirection = 'start',
  animateOn = 'mount',
  className = '',
  style = {},
  onComplete
}) => {
  const [displayText, setDisplayText] = useState('');
  const [isAnimating, setIsAnimating] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const elementRef = useRef<HTMLSpanElement>(null);
  const animationRef = useRef<NodeJS.Timeout | null>(null);

  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';

  const getRandomChar = () => characters[Math.floor(Math.random() * characters.length)];

  const startAnimation = () => {
    if (isAnimating || hasAnimated) return;
    
    setIsAnimating(true);
    const textLength = text.length;
    const iterations = Array(textLength).fill(0);
    const revealed = Array(textLength).fill(false);
    
    // Determine reveal order based on direction
    let revealOrder: number[] = [];
    switch (revealDirection) {
      case 'start':
        revealOrder = Array.from({ length: textLength }, (_, i) => i);
        break;
      case 'end':
        revealOrder = Array.from({ length: textLength }, (_, i) => textLength - 1 - i);
        break;
      case 'center':
        const center = Math.floor(textLength / 2);
        revealOrder = [center];
        for (let i = 1; i <= Math.max(center, textLength - center - 1); i++) {
          if (center - i >= 0) revealOrder.push(center - i);
          if (center + i < textLength) revealOrder.push(center + i);
        }
        break;
      case 'random':
        revealOrder = Array.from({ length: textLength }, (_, i) => i);
        for (let i = revealOrder.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [revealOrder[i], revealOrder[j]] = [revealOrder[j], revealOrder[i]];
        }
        break;
    }

    let currentRevealIndex = 0;

    const animate = () => {
      let newDisplayText = '';
      let allRevealed = true;

      for (let i = 0; i < textLength; i++) {
        if (revealed[i]) {
          newDisplayText += text[i];
        } else {
          allRevealed = false;
          if (text[i] === ' ') {
            newDisplayText += ' ';
          } else {
            newDisplayText += getRandomChar();
          }
        }
      }

      setDisplayText(newDisplayText);

      // Handle sequential revealing
      if (sequential && currentRevealIndex < revealOrder.length) {
        const indexToReveal = revealOrder[currentRevealIndex];
        iterations[indexToReveal]++;
        
        if (iterations[indexToReveal] >= maxIterations) {
          revealed[indexToReveal] = true;
          currentRevealIndex++;
        }
      } else if (!sequential) {
        // Handle simultaneous revealing
        for (let i = 0; i < textLength; i++) {
          if (!revealed[i] && text[i] !== ' ') {
            iterations[i]++;
            if (iterations[i] >= maxIterations) {
              revealed[i] = true;
            }
          }
        }
      }

      if (allRevealed) {
        setIsAnimating(false);
        setHasAnimated(true);
        if (onComplete) onComplete();
      } else {
        animationRef.current = setTimeout(animate, speed);
      }
    };

    animate();
  };

  useEffect(() => {
    if (animateOn === 'mount') {
      startAnimation();
    }
  }, [text, animateOn]);

  useEffect(() => {
    if (animateOn === 'view') {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting && !hasAnimated) {
              startAnimation();
            }
          });
        },
        { threshold: 0.1 }
      );

      if (elementRef.current) {
        observer.observe(elementRef.current);
      }

      return () => {
        if (elementRef.current) {
          observer.unobserve(elementRef.current);
        }
      };
    }
  }, [animateOn, hasAnimated]);

  useEffect(() => {
    return () => {
      if (animationRef.current) {
        clearTimeout(animationRef.current);
      }
    };
  }, []);

  // If not animating and hasn't animated yet, show the original text
  const finalText = hasAnimated || !isAnimating ? text : displayText;

  return (
    <span
      ref={elementRef}
      className={className}
      style={style}
    >
      {finalText}
    </span>
  );
};

export default DecryptedText;
