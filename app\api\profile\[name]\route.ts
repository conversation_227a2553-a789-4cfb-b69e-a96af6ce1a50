import { NextRequest } from 'next/server';

interface Context {
  params: Promise<{ name: string }>;
}

export async function GET(
  _request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { name } = await context.params;

    // Check if name is undefined, null, or empty
    if (!name || name === 'undefined') {
      console.error('Invalid name parameter:', name);
      return Response.json(
        { error: 'Valid name or address is required' },
        { status: 400 }
      );
    }

    // During build time, return a mock response
    if (process.env.NODE_ENV !== 'development' && !process.env.VERCEL_ENV) {
      return Response.json({
        error: 'Profile not available during build'
      }, { status: 404 });
    }

    // Import database modules only when needed
    const { db } = await import('@/db/drizzle');
    const { web3Profile, componentPositions } = await import('@/db/schema');
    const { eq } = await import('drizzle-orm');

    console.log(`Fetching profile for: ${name}`);

    // Check if the input is an Ethereum address (0x...)
    const isAddress = name.startsWith('0x') && name.length >= 40;

    let profile: any;
    let matchingComponents: any[] = [];

    if (isAddress) {
      // If it's an address, get profile data directly
      console.log(`Treating ${name} as an address`);

      profile = await db
        .select()
        .from(web3Profile)
        .where(eq(web3Profile.address, name));

      if (profile.length === 0) {
        // If not found by address, try looking up by name as a fallback
        console.log(`Profile not found by address, trying as name: ${name}`);

        const profilesByName = await db
          .select()
          .from(web3Profile)
          .where(eq(web3Profile.name, name));

        if (profilesByName.length > 0) {
          profile = profilesByName;
        }
      }
    } else {
      // If it's a name, search by name first
      console.log(`Treating ${name} as a name`);

      const profilesByName = await db
        .select()
        .from(web3Profile)
        .where(eq(web3Profile.name, name));

      if (profilesByName.length > 0) {
        console.log(`Found profile by name in web3Profile table: ${name}`);
        profile = profilesByName;
      } else {
        // If not found in web3Profile table, try looking up by name in bannerpfp component
        console.log(`Profile not found by name in web3Profile table, trying in bannerpfp components: ${name}`);

        // Find a bannerpfp component with this name in the details field
        const bannerpfpComponents = await db
          .select()
          .from(componentPositions)
          .where(eq(componentPositions.componentType, 'bannerpfp'));

        // Filter components that have the matching name in their details
        matchingComponents = bannerpfpComponents.filter(component => {
          if (component.details && typeof component.details === 'object') {
            const details = component.details as any;
            return details.profileName === name || details.urlName === name;
          }
          return false;
        });

        console.log(`Found ${matchingComponents.length} matching bannerpfp components`);
      }
    }

    // If we found matching components but no profile, get the profile by address
    if ((!profile || profile.length === 0) && matchingComponents.length > 0) {
      const matchingAddress = matchingComponents[0].address;
      console.log(`Found matching component for address: ${matchingAddress}`);

      profile = await db
        .select()
        .from(web3Profile)
        .where(eq(web3Profile.address, matchingAddress));

      if (profile.length === 0) {
        console.log(`No profile found for address: ${matchingAddress}`);
        return Response.json(
          { error: 'Profile not found' },
          { status: 404 }
        );
      }

      console.log(`Found profile by name: ${matchingAddress}`);
    }

    // If still no profile found
    if (!profile || profile.length === 0) {
      console.log(`No profile found for: ${name}`);
      return Response.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    const profileData = profile[0];
    console.log(`Profile found: ${profileData.address}`);

    // Only show approved profiles to public viewers
    if (profileData.status !== 'approved') {
      console.log(`Profile ${profileData.address} is not approved (status: ${profileData.status})`);
      return Response.json(
        { error: 'Profile not available' },
        { status: 404 }
      );
    }

    // Get all components for this profile
    const components = await db
      .select()
      .from(componentPositions)
      .where(eq(componentPositions.address, profileData.address));

    console.log(`Found ${components.length} components for profile`);

    // Filter out hidden components and sort by order
    const visibleComponents = components
      .filter(c => c.hidden !== 'Y')
      .sort((a, b) => parseInt(a.order) - parseInt(b.order));

    // Return profile data with components
    return Response.json({
      address: profileData.address,
      name: profileData.name,
      chain: profileData.chain,
      status: profileData.status,
      components: visibleComponents.map(c => {
        const details = c.details as any;
        return {
          componentType: c.componentType,
          order: c.order,
          hidden: c.hidden,
          backgroundColor: details?.backgroundColor,
          fontColor: details?.fontColor,
          socialLinks: details?.socialLinks,
          heroContent: details?.heroContent,
          profileName: c.componentType === 'bannerpfp' ? details?.profileName : undefined,
          profileBio: c.componentType === 'bannerpfp' ? details?.profileBio : undefined,
          profileShape: c.componentType === 'bannerpfp' ? details?.profileShape : undefined,
          profileNameStyle: c.componentType === 'bannerpfp' ? details?.profileNameStyle : undefined,
          profileHorizontalPosition: c.componentType === 'bannerpfp' ? details?.profileHorizontalPosition : undefined,
          profileNameHorizontalPosition: c.componentType === 'bannerpfp' ? details?.profileNameHorizontalPosition : undefined,
          details: c.details
        };
      }),
    });
  } catch (error: any) {
    console.error('Failed to fetch profile:', error);

    // Check for database connection error
    if (error.code === 'ECONNREFUSED') {
      return Response.json(
        { error: 'Database is currently unavailable. Please try again later.' },
        { status: 503 }
      );
    }

    return Response.json(
      { error: 'Failed to fetch profile' },
      { status: 500 }
    );
  }
}
