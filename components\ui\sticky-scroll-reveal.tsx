'use client';

import React, { useEffect, useRef, useState } from "react";
import { useMotionValueEvent, useScroll } from "framer-motion";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

export const StickyScrollReveal = ({
  content,
  contentClassName,
  fontColor,
  backgroundColor,
}: {
  content: {
    title: string;
    description: string;
    content?: React.ReactNode | any;
    customBackground?: string;
    customScrollContainerClass?: string;
  }[];
  contentClassName?: string;
  fontColor?: string;
  backgroundColor?: string;
}) => {
  const [activeCard, setActiveCard] = React.useState(0);
  const ref = useRef<any>(null);
  const { scrollYProgress } = useScroll({
    container: ref,
    offset: ["start start", "end start"],
  });
  const cardLength = content.length;

  useMotionValueEvent(scrollYProgress, "change", (latest) => {
    const cardsBreakpoints = content.map((_, index) => index / cardLength);
    const closestBreakpointIndex = cardsBreakpoints.reduce(
      (acc, breakpoint, index) => {
        const distance = Math.abs(latest - breakpoint);
        if (distance < Math.abs(latest - cardsBreakpoints[acc])) {
          return index;
        }
        return acc;
      },
      0
    );
    setActiveCard(closestBreakpointIndex);
  });

  const backgroundStyle = backgroundColor ? { backgroundColor } : {};
  const textStyle = fontColor ? { color: fontColor } : {};

  return (
    <motion.div
      className="h-[20rem] sm:h-[26rem] md:h-[28rem] overflow-y-auto flex justify-center relative space-x-10 rounded-md p-10"
      ref={ref}
      style={backgroundStyle}
    >
      <div className="div relative flex items-start px-4">
        <div className="max-w-2xl">
          {content.map((item, index) => (
            <div key={item.title + index} className="my-20">
              <motion.h2
                initial={{
                  opacity: 0,
                }}
                animate={{
                  opacity: activeCard === index ? 1 : 0.3,
                }}
                className="text-2xl font-bold text-slate-100"
                style={textStyle}
              >
                {item.title}
              </motion.h2>
              <motion.p
                initial={{
                  opacity: 0,
                }}
                animate={{
                  opacity: activeCard === index ? 1 : 0.3,
                }}
                className="text-kg text-slate-300 max-w-sm mt-10"
                style={textStyle}
              >
                {item.description}
              </motion.p>
            </div>
          ))}
          <div className="h-40" />
        </div>
      </div>
      <div
        className={cn(
          "hidden lg:block h-60 w-80 rounded-md bg-white sticky top-10 overflow-hidden",
          contentClassName
        )}
      >
        {content[activeCard]?.content ?? null}
      </div>
    </motion.div>
  );
};

export const StickyScrollRevealDemo = ({
  content,
  fontColor,
  backgroundColor,
}: {
  content: {
    title: string;
    description: string;
    content?: React.ReactNode | any;
    customBackground?: string;
    customScrollContainerClass?: string;
  }[];
  fontColor?: string;
  backgroundColor?: string;
}) => {
  return (
    <div className="p-0">
      <StickyScrollReveal 
        content={content} 
        fontColor={fontColor}
        backgroundColor={backgroundColor}
      />
    </div>
  );
};
