import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Web3Socials Profile Viewer",
  description: "View Web3 social profiles",
  keywords: ["web3", "social", "profile", "blockchain", "crypto"],
  authors: [{ name: "Web3Socials" }],
  openGraph: {
    title: "Web3Socials Profile Viewer",
    description: "View Web3 social profiles",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Web3Socials Profile Viewer",
    description: "View Web3 social profiles",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased profile-viewer`}
      >
        <div className="relative min-h-screen">
          {children}
        </div>
      </body>
    </html>
  );
}
