'use client';

import React, { useState, useEffect } from 'react';
import { StickyScrollRevealDemo } from '@/components/ui/sticky-scroll-reveal';
import { HeaderTypewriterEffect } from '@/components/ui/header-typewriter-effect';
import DecryptedText from '@/components/ui/DecryptedText';

interface HeroProps {
  address: string;
  backgroundColor?: string;
  fontColor?: string;
  heroContent?: any[];
}

interface ImageData {
  id: string;
  imageData: string | null;
  scale: string;
  positionX: number;
  positionY: number;
  naturalWidth: number | null;
  naturalHeight: number | null;
}

const RenderHero: React.FC<HeroProps> = ({
  address,
  backgroundColor = '#000000',
  fontColor = '#ffffff',
  heroContent = [],
}) => {
  const [heroImages, setHeroImages] = useState<{ [key: string]: ImageData }>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchHeroImages = async () => {
      try {
        // Fetch images via API to avoid client-side database calls
        const response = await fetch(`/api/images/${address}/hero`);
        if (response.ok) {
          const images = await response.json();

          const imagesMap: { [key: string]: ImageData } = {};
          images.forEach((img: any) => {
            if (img.section) {
              imagesMap[img.section] = img;
            }
          });

          setHeroImages(imagesMap);
        }
      } catch (error) {
        console.error('Error fetching hero images:', error);
      } finally {
        setLoading(false);
      }
    };

    if (address) {
      fetchHeroImages();
    }
  }, [address]);

  const renderTextWithEffect = (text: string, effect: string, className: string = '') => {
    if (!text) return null;

    const style = { color: fontColor };

    switch (effect) {
      case 'typewriter effect':
        return (
          <HeaderTypewriterEffect
            words={[{ text, className: className }]}
            className={className}
            style={style}
          />
        );
      case 'decrypted effect':
        return (
          <DecryptedText
            text={text}
            className={className}
            style={style}
            speed={50}
            maxIterations={8}
            sequential={true}
            revealDirection="start"
            animateOn="view"
          />
        );
      default:
        return (
          <div className={className} style={style}>
            {text}
          </div>
        );
    }
  };

  const renderHeroSection = (section: any, index: number) => {
    const sectionImage = heroImages[index.toString()];

    if (section.type === 'sticky-scroll') {
      // Transform the content for sticky scroll
      const stickyContent = section.content?.map((item: any) => ({
        title: item.title || '',
        description: item.description || '',
        content: sectionImage?.imageData ? (
          <div className="h-full w-full bg-gradient-to-br from-cyan-500 to-emerald-500 flex items-center justify-center">
            <img
              src={sectionImage.imageData}
              alt={item.title || 'Hero content'}
              className="w-full h-full object-cover rounded-lg"
              style={{
                transform: `scale(${sectionImage.scale}) translate(${sectionImage.positionX}px, ${sectionImage.positionY}px)`,
                transformOrigin: 'center center',
              }}
            />
          </div>
        ) : (
          <div className="h-full w-full bg-gradient-to-br from-cyan-500 to-emerald-500 flex items-center justify-center text-white">
            <p>No image available</p>
          </div>
        ),
      })) || [];

      return (
        <StickyScrollRevealDemo
          content={stickyContent}
          fontColor={fontColor}
          backgroundColor={backgroundColor}
        />
      );
    }

    // Regular hero section
    return (
      <div
        className="relative min-h-[400px] flex items-center justify-center p-8 overflow-hidden"
        style={{ backgroundColor }}
      >
        {/* Background Image */}
        {sectionImage?.imageData && (
          <div className="absolute inset-0">
            <img
              src={sectionImage.imageData}
              alt="Hero background"
              className="w-full h-full object-cover"
              style={{
                transform: `scale(${sectionImage.scale}) translate(${sectionImage.positionX}px, ${sectionImage.positionY}px)`,
                transformOrigin: 'center center',
              }}
            />
            <div className="absolute inset-0 bg-black bg-opacity-40"></div>
          </div>
        )}

        {/* Content */}
        <div className="relative z-10 text-center max-w-4xl mx-auto">
          {section.title && renderTextWithEffect(
            section.title,
            section.titleEffect || 'no effect',
            'text-4xl md:text-6xl font-bold mb-6'
          )}

          {section.subtitle && renderTextWithEffect(
            section.subtitle,
            section.subtitleEffect || 'no effect',
            'text-xl md:text-2xl mb-8 opacity-90'
          )}

          {section.description && (
            <div
              className="text-lg md:text-xl opacity-80 max-w-2xl mx-auto"
              style={{ color: fontColor }}
            >
              {section.description}
            </div>
          )}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div
        className="relative w-full min-h-[400px] flex items-center justify-center"
        style={{ backgroundColor }}
      >
        <div className="loading-pulse text-white">Loading hero content...</div>
      </div>
    );
  }

  if (!heroContent || heroContent.length === 0) {
    return null;
  }

  return (
    <div className="w-full">
      {heroContent.map((section, index) => (
        <div key={index}>
          {renderHeroSection(section, index)}
        </div>
      ))}
    </div>
  );
};

export default RenderHero;
