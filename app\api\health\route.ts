import { NextRequest } from 'next/server';

export async function GET(_request: NextRequest): Promise<Response> {
  try {
    // During build time, skip database connection
    if (process.env.NODE_ENV === 'development' || process.env.VERCEL_ENV === 'production') {
      const { db } = await import('@/db/drizzle');
      const { sql } = await import('drizzle-orm');

      // Test database connection
      await db.execute(sql`SELECT 1 as test`);

      return Response.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        database: 'connected',
        version: process.env.npm_package_version || '1.0.0'
      });
    } else {
      // During build, return a basic health check
      return Response.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        database: 'build-mode',
        version: process.env.npm_package_version || '1.0.0'
      });
    }
  } catch (error: any) {
    console.error('Health check failed:', error);

    return Response.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: error.message
    }, { status: 503 });
  }
}
