import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { sql } from 'drizzle-orm';

export async function GET(_request: NextRequest): Promise<Response> {
  try {
    // Test database connection
    await db.execute(sql`SELECT 1 as test`);
    
    return Response.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: 'connected',
      version: process.env.npm_package_version || '1.0.0'
    });
  } catch (error: any) {
    console.error('Health check failed:', error);
    
    return Response.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: error.message
    }, { status: 503 });
  }
}
