'use client';

import React, { useState, useEffect } from 'react';
import DecryptedText from '@/components/ui/DecryptedText';

interface BannerPfpProps {
  address: string;
  backgroundColor?: string;
  fontColor?: string;
  profileName?: string;
  profileBio?: string;
  profileShape?: string;
  profileNameStyle?: string;
  profileHorizontalPosition?: string;
  profileNameHorizontalPosition?: string;
}

interface ImageData {
  id: string;
  imageData: string | null;
  scale: string;
  positionX: number;
  positionY: number;
  naturalWidth: number | null;
  naturalHeight: number | null;
}

const RenderBannerPfp: React.FC<BannerPfpProps> = ({
  address,
  backgroundColor = '#000000',
  fontColor = '#ffffff',
  profileName = '',
  profileBio = '',
  profileShape = 'circle',
  profileNameStyle = 'no effect',
  profileHorizontalPosition = 'center',
  profileNameHorizontalPosition = 'center',
}) => {
  const [bannerImage, setBannerImage] = useState<ImageData | null>(null);
  const [profileImage, setProfileImage] = useState<ImageData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchImages = async () => {
      try {
        // Fetch images via API to avoid client-side database calls
        const response = await fetch(`/api/images/${address}/bannerpfp`);
        if (response.ok) {
          const images = await response.json();

          const banner = images.find((img: any) => img.section === 'banner');
          const profile = images.find((img: any) => img.section === 'profile');

          if (banner) setBannerImage(banner);
          if (profile) setProfileImage(profile);
        }
      } catch (error) {
        console.error('Error fetching images:', error);
      } finally {
        setLoading(false);
      }
    };

    if (address) {
      fetchImages();
    }
  }, [address]);

  const getHorizontalPositionClass = (position: string) => {
    switch (position) {
      case 'left':
        return 'justify-start';
      case 'right':
        return 'justify-end';
      case 'center':
      default:
        return 'justify-center';
    }
  };

  const getProfileShapeClass = (shape: string) => {
    switch (shape) {
      case 'square':
        return 'rounded-lg';
      case 'circle':
      default:
        return 'rounded-full';
    }
  };

  const renderProfileName = () => {
    if (!profileName) return null;

    const baseClasses = "text-2xl md:text-3xl font-bold mb-2";
    const style = { color: fontColor };

    if (profileNameStyle === 'decrypted effect') {
      return (
        <DecryptedText
          text={profileName}
          className={baseClasses}
          style={style}
          speed={50}
          maxIterations={8}
          sequential={true}
          revealDirection="start"
          animateOn="view"
        />
      );
    }

    return (
      <h1 className={baseClasses} style={style}>
        {profileName}
      </h1>
    );
  };

  if (loading) {
    return (
      <div
        className="relative w-full h-96 flex items-center justify-center"
        style={{ backgroundColor }}
      >
        <div className="loading-pulse text-white">Loading profile...</div>
      </div>
    );
  }

  return (
    <div
      className="relative w-full h-96 overflow-hidden"
      style={{ backgroundColor }}
    >
      {/* Banner Image */}
      {bannerImage?.imageData && (
        <div className="absolute inset-0">
          <img
            src={bannerImage.imageData}
            alt="Banner"
            className="w-full h-full object-cover"
            style={{
              transform: `scale(${bannerImage.scale}) translate(${bannerImage.positionX}px, ${bannerImage.positionY}px)`,
              transformOrigin: 'center center',
            }}
          />
        </div>
      )}

      {/* Content Overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center">
        <div className="w-full px-6 md:px-12">
          <div className={`flex items-center gap-6 ${getHorizontalPositionClass(profileHorizontalPosition)}`}>
            {/* Profile Image */}
            {profileImage?.imageData && (
              <div className={`w-24 h-24 md:w-32 md:h-32 overflow-hidden ${getProfileShapeClass(profileShape)} border-4 border-white shadow-lg flex-shrink-0`}>
                <img
                  src={profileImage.imageData}
                  alt="Profile"
                  className="w-full h-full object-cover"
                  style={{
                    transform: `scale(${profileImage.scale}) translate(${profileImage.positionX}px, ${profileImage.positionY}px)`,
                    transformOrigin: 'center center',
                  }}
                />
              </div>
            )}

            {/* Profile Info */}
            <div className={`flex flex-col ${getHorizontalPositionClass(profileNameHorizontalPosition)}`}>
              {renderProfileName()}

              {profileBio && (
                <p
                  className="text-lg md:text-xl opacity-90 max-w-md"
                  style={{ color: fontColor }}
                >
                  {profileBio}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RenderBannerPfp;
