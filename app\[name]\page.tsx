import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import ProfileViewer from '@/components/ProfileViewer';

interface PageProps {
  params: Promise<{ name: string }>;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { name } = await params;
  
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/profile/${name}`, {
      cache: 'no-store'
    });
    
    if (!response.ok) {
      return {
        title: 'Profile Not Found - Web3Socials',
        description: 'The requested profile could not be found.',
      };
    }
    
    const profileData = await response.json();
    const profileName = profileData.components?.find((c: any) => c.componentType === 'bannerpfp')?.profileName || profileData.name || name;
    const profileBio = profileData.components?.find((c: any) => c.componentType === 'bannerpfp')?.profileBio || '';
    
    return {
      title: `${profileName} - Web3Socials Profile`,
      description: profileBio || `View ${profileName}'s Web3 social profile`,
      keywords: ['web3', 'social', 'profile', 'blockchain', 'crypto', profileName],
      authors: [{ name: profileName }],
      openGraph: {
        title: `${profileName} - Web3Socials Profile`,
        description: profileBio || `View ${profileName}'s Web3 social profile`,
        type: 'profile',
        url: `${process.env.NEXT_PUBLIC_SITE_URL}/${name}`,
      },
      twitter: {
        card: 'summary_large_image',
        title: `${profileName} - Web3Socials Profile`,
        description: profileBio || `View ${profileName}'s Web3 social profile`,
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Web3Socials Profile',
      description: 'View Web3 social profiles',
    };
  }
}

export default async function ProfilePage({ params }: PageProps) {
  const { name } = await params;
  
  // Validate the name parameter
  if (!name || name === 'undefined' || name.trim() === '') {
    notFound();
  }

  try {
    // Fetch profile data from our API
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/profile/${name}`, {
      cache: 'no-store', // Always fetch fresh data
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        notFound();
      }
      throw new Error(`Failed to fetch profile: ${response.status}`);
    }

    const profileData = await response.json();

    return <ProfileViewer profileData={profileData} />;
  } catch (error) {
    console.error('Error fetching profile:', error);
    
    // Show a more user-friendly error page
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <h1 className="text-4xl font-bold mb-4">Oops!</h1>
          <p className="text-xl mb-6 text-neutral-300">
            We couldn't load this profile right now.
          </p>
          <p className="text-neutral-400 mb-8">
            This could be due to a temporary issue or the profile might not exist.
          </p>
          <div className="space-y-4">
            <a
              href="/"
              className="inline-block px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
            >
              Go Home
            </a>
            <br />
            <a
              href={process.env.NEXT_PUBLIC_MAIN_SITE_URL || "https://web3socials.fun"}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block px-6 py-3 bg-neutral-800 hover:bg-neutral-700 text-white rounded-lg font-medium transition-colors border border-neutral-700"
            >
              Visit Web3Socials
            </a>
          </div>
        </div>
      </div>
    );
  }
}
