import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatAddress(address: string): string {
  if (!address) return '';
  return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
}

export function isValidAddress(address: string): boolean {
  return address.startsWith('0x') && address.length >= 40;
}

export function generateProfileUrl(nameOrAddress: string): string {
  return `/${nameOrAddress}`;
}
