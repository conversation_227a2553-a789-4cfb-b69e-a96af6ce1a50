'use client';

import React, { useState, useEffect } from 'react';
import { FloatingDock } from '@/components/ui/floating-dock';
import {
  Globe,
  Twitter,
  Instagram,
  Github,
  Linkedin,
  Youtube,
  Facebook,
  MessageCircle,
  Mail,
  Phone,
  MapPin,
  ExternalLink,
  Copy,
  Users
} from 'lucide-react';
// Removed direct database imports - using API calls instead

interface SocialLinksProps {
  address: string;
  backgroundColor?: string;
  fontColor?: string;
  socialLinks?: any[];
  referralCode?: string;
}

const RenderSocialLinks: React.FC<SocialLinksProps> = ({
  address,
  backgroundColor = '#000000',
  fontColor = '#ffffff',
  socialLinks = [],
  referralCode,
}) => {
  const [referralCount, setReferralCount] = useState(0);
  const [copiedReferral, setCopiedReferral] = useState(false);

  useEffect(() => {
    const fetchReferralCount = async () => {
      if (!referralCode) return;

      try {
        const response = await fetch(`/api/referrals/${referralCode}/count`);
        if (response.ok) {
          const data = await response.json();
          setReferralCount(data.count);
        }
      } catch (error) {
        console.error('Error fetching referral count:', error);
      }
    };

    fetchReferralCount();
  }, [referralCode]);

  const getIconForPlatform = (platform: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      website: <Globe className="h-5 w-5" />,
      twitter: <Twitter className="h-5 w-5" />,
      instagram: <Instagram className="h-5 w-5" />,
      github: <Github className="h-5 w-5" />,
      linkedin: <Linkedin className="h-5 w-5" />,
      youtube: <Youtube className="h-5 w-5" />,
      facebook: <Facebook className="h-5 w-5" />,
      discord: <MessageCircle className="h-5 w-5" />,
      telegram: <MessageCircle className="h-5 w-5" />,
      email: <Mail className="h-5 w-5" />,
      phone: <Phone className="h-5 w-5" />,
      location: <MapPin className="h-5 w-5" />,
    };

    return iconMap[platform.toLowerCase()] || <ExternalLink className="h-5 w-5" />;
  };

  const formatUrl = (url: string, platform: string) => {
    if (!url) return '#';

    // Handle special cases
    if (platform.toLowerCase() === 'email' && !url.startsWith('mailto:')) {
      return `mailto:${url}`;
    }

    if (platform.toLowerCase() === 'phone' && !url.startsWith('tel:')) {
      return `tel:${url}`;
    }

    // Add https:// if no protocol is specified
    if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('mailto:') && !url.startsWith('tel:')) {
      return `https://${url}`;
    }

    return url;
  };

  const copyReferralCode = async () => {
    if (!referralCode) return;

    try {
      await navigator.clipboard.writeText(referralCode);
      setCopiedReferral(true);
      setTimeout(() => setCopiedReferral(false), 2000);
    } catch (error) {
      console.error('Failed to copy referral code:', error);
    }
  };

  // Filter out empty social links
  const validSocialLinks = socialLinks.filter(link => link.url && link.url.trim() !== '');

  // Prepare items for FloatingDock
  const dockItems = validSocialLinks.map(link => ({
    title: link.platform || 'Link',
    icon: getIconForPlatform(link.platform || ''),
    href: formatUrl(link.url, link.platform || ''),
  }));

  if (validSocialLinks.length === 0 && !referralCode) {
    return null;
  }

  return (
    <div
      className="w-full py-12 px-6"
      style={{ backgroundColor }}
    >
      <div className="max-w-4xl mx-auto">
        {/* Social Links */}
        {validSocialLinks.length > 0 && (
          <div className="mb-8">
            <h3
              className="text-2xl font-bold text-center mb-8"
              style={{ color: fontColor }}
            >
              Connect With Me
            </h3>

            <div className="flex justify-center">
              <FloatingDock
                items={dockItems}
                backgroundColor={backgroundColor}
                fontColor={fontColor}
              />
            </div>
          </div>
        )}

        {/* Referral Code Section */}
        {referralCode && (
          <div className="text-center">
            <div
              className="inline-block p-6 rounded-lg border border-neutral-700"
              style={{
                backgroundColor: backgroundColor,
                borderColor: fontColor + '30'
              }}
            >
              <div className="flex items-center gap-3 mb-3">
                <Users className="h-5 w-5" style={{ color: fontColor }} />
                <h4
                  className="text-lg font-semibold"
                  style={{ color: fontColor }}
                >
                  Referral Code
                </h4>
              </div>

              <div className="flex items-center gap-3 mb-2">
                <code
                  className="text-xl font-mono px-4 py-2 rounded border"
                  style={{
                    backgroundColor: 'transparent',
                    color: fontColor,
                    borderColor: fontColor + '50'
                  }}
                >
                  {referralCode}
                </code>

                <button
                  onClick={copyReferralCode}
                  className="p-2 rounded hover:bg-neutral-700 transition-colors"
                  title="Copy referral code"
                >
                  <Copy
                    className="h-4 w-4"
                    style={{ color: fontColor }}
                  />
                </button>
              </div>

              {copiedReferral && (
                <p
                  className="text-sm opacity-75"
                  style={{ color: fontColor }}
                >
                  Copied to clipboard!
                </p>
              )}

              {referralCount > 0 && (
                <p
                  className="text-sm opacity-75 mt-2"
                  style={{ color: fontColor }}
                >
                  {referralCount} user{referralCount !== 1 ? 's' : ''} joined using this code
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RenderSocialLinks;
