import Link from "next/link";
import { Search, Home, ExternalLink } from "lucide-react";

export default function NotFound() {
  const mainSiteUrl = process.env.NEXT_PUBLIC_MAIN_SITE_URL || "https://web3socials.fun";

  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center">
      <div className="text-center max-w-2xl mx-auto p-6">
        {/* 404 Icon */}
        <div className="mb-8">
          <div className="w-24 h-24 mx-auto bg-gradient-to-br from-red-500 to-orange-600 rounded-full flex items-center justify-center">
            <Search className="w-12 h-12 text-white" />
          </div>
        </div>

        {/* Error Message */}
        <h1 className="text-6xl md:text-8xl font-bold mb-4 bg-gradient-to-r from-red-400 to-orange-600 bg-clip-text text-transparent">
          404
        </h1>
        
        <h2 className="text-2xl md:text-3xl font-bold mb-4">
          Profile Not Found
        </h2>
        
        <p className="text-lg md:text-xl text-neutral-300 mb-8 leading-relaxed">
          The profile you're looking for doesn't exist or isn't available for public viewing.
        </p>

        {/* Help Information */}
        <div className="bg-neutral-900/50 border border-neutral-800 rounded-lg p-6 mb-8 text-left">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Search className="w-5 h-5" />
            What you can try:
          </h3>
          <ul className="space-y-2 text-neutral-300">
            <li>• Check if the username or address is spelled correctly</li>
            <li>• Make sure the profile has been approved and is public</li>
            <li>• Try using the full wallet address instead of username</li>
            <li>• Visit the main Web3Socials site to search for profiles</li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/"
              className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
            >
              <Home className="w-4 h-4" />
              Go Home
            </Link>
            
            <Link
              href={`${mainSiteUrl}/discover`}
              className="inline-flex items-center gap-2 px-6 py-3 bg-neutral-800 hover:bg-neutral-700 text-white rounded-lg font-medium transition-colors border border-neutral-700"
            >
              <Search className="w-4 h-4" />
              Browse Profiles
            </Link>
          </div>
          
          <Link
            href={`${mainSiteUrl}/createpage`}
            className="inline-flex items-center gap-2 px-4 py-2 text-blue-400 hover:text-blue-300 transition-colors"
          >
            <ExternalLink className="w-4 h-4" />
            Create Your Own Profile
          </Link>
        </div>

        {/* Footer */}
        <div className="pt-8 mt-8 border-t border-neutral-800">
          <p className="text-sm text-neutral-500">
            Powered by{" "}
            <Link 
              href={mainSiteUrl} 
              className="text-blue-400 hover:text-blue-300 transition-colors"
            >
              Web3Socials
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
