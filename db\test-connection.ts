import { config } from "dotenv";
import { db } from './drizzle';
import { sql } from 'drizzle-orm';
import { web3Profile } from './schema';

config({ path: ".env" });

/**
 * Test script to verify database connection and check for profiles
 */
async function testConnection() {
  try {
    console.log('Testing database connection...');
    console.log('Connection string:', process.env.DATABASE_URL?.replace(/(mysql:\/\/[^:]+:)[^@]+(@.+)/, '$1*****$2')); // Hide password

    // Try a simple query
    const result = await db.execute(sql`SELECT 1 as test`);
    console.log('✅ Database connection successful!');
    console.log('Test query result:', result);

    // Check for profiles
    console.log('\nChecking for profiles...');
    const profiles = await db.select().from(web3Profile).limit(5);
    
    if (profiles.length > 0) {
      console.log(`✅ Found ${profiles.length} profiles (showing first 5):`);
      profiles.forEach((profile, index) => {
        console.log(`${index + 1}. Address: ${profile.address}`);
        console.log(`   Name: ${profile.name || 'No name set'}`);
        console.log(`   Chain: ${profile.chain}`);
        console.log(`   Status: ${profile.status}`);
        console.log('');
      });
    } else {
      console.log('⚠️  No profiles found in database');
    }

    console.log('✅ Database test completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    process.exit(1);
  }
}

testConnection();
