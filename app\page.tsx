import Link from "next/link";
import { ExternalLink, Users, Globe } from "lucide-react";

export default function Home() {
  const mainSiteUrl = process.env.NEXT_PUBLIC_MAIN_SITE_URL || "https://web3socials.fun";

  return (
    <main className="min-h-screen flex items-center justify-center p-4">
      <div className="max-w-2xl mx-auto text-center space-y-8">
        {/* Logo/Title */}
        <div className="space-y-4">
          <div className="w-20 h-20 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <Users className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent">
            Web3Socials
          </h1>
          <p className="text-xl text-neutral-400">Profile Viewer</p>
        </div>

        {/* Description */}
        <div className="space-y-4">
          <p className="text-lg text-neutral-300 leading-relaxed">
            This is a lightweight profile viewer for Web3Socials. 
            To view a profile, visit <code className="bg-neutral-800 px-2 py-1 rounded text-blue-400">/username</code> or <code className="bg-neutral-800 px-2 py-1 rounded text-blue-400">/0x...</code>
          </p>
          
          <div className="bg-neutral-900/50 border border-neutral-800 rounded-lg p-6 space-y-3">
            <h3 className="text-lg font-semibold text-white flex items-center gap-2">
              <Globe className="w-5 h-5" />
              How to use
            </h3>
            <div className="text-left space-y-2 text-neutral-300">
              <p>• <strong>By username:</strong> <code className="text-blue-400">yourdomain.com/john</code></p>
              <p>• <strong>By wallet address:</strong> <code className="text-blue-400">yourdomain.com/0x123...</code></p>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href={mainSiteUrl}
              className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
            >
              <ExternalLink className="w-4 h-4" />
              Visit Web3Socials
            </Link>
            
            <Link
              href={`${mainSiteUrl}/createpage`}
              className="inline-flex items-center gap-2 px-6 py-3 bg-neutral-800 hover:bg-neutral-700 text-white rounded-lg font-medium transition-colors border border-neutral-700"
            >
              <Users className="w-4 h-4" />
              Create Profile
            </Link>
          </div>
          
          <p className="text-sm text-neutral-500">
            Don't have a profile yet? Create one on the main Web3Socials platform.
          </p>
        </div>

        {/* Footer */}
        <div className="pt-8 border-t border-neutral-800">
          <p className="text-sm text-neutral-500">
            Powered by{" "}
            <Link 
              href={mainSiteUrl} 
              className="text-blue-400 hover:text-blue-300 transition-colors"
            >
              Web3Socials
            </Link>
          </p>
        </div>
      </div>
    </main>
  );
}
