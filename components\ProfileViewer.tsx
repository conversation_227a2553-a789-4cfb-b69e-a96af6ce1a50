'use client';

import React from 'react';
import RenderBannerPfp from './renders/render_bannerpfp';
import RenderHero from './renders/render_hero';
import RenderSocialLinks from './renders/render_sociallinks';

interface ProfileData {
  address: string;
  name: string | null;
  chain: string;
  status: string;
  components: Array<{
    componentType: string;
    order: string;
    hidden: string;
    backgroundColor?: string;
    fontColor?: string;
    socialLinks?: any[];
    heroContent?: any[];
    profileName?: string;
    profileBio?: string;
    profileShape?: string;
    profileNameStyle?: string;
    profileHorizontalPosition?: string;
    profileNameHorizontalPosition?: string;
    details?: any;
  }>;
}

interface ProfileViewerProps {
  profileData: ProfileData;
}

const ProfileViewer: React.FC<ProfileViewerProps> = ({ profileData }) => {
  const { address, name, chain, components } = profileData;

  // Sort components by order
  const sortedComponents = [...components].sort((a, b) => parseInt(a.order) - parseInt(b.order));

  const renderComponent = (component: any) => {
    const {
      componentType,
      backgroundColor,
      fontColor,
      socialLinks,
      heroContent,
      profileName,
      profileBio,
      profileShape,
      profileNameStyle,
      profileHorizontalPosition,
      profileNameHorizontalPosition,
      details
    } = component;

    switch (componentType) {
      case 'bannerpfp':
        return (
          <RenderBannerPfp
            key={`${componentType}-${component.order}`}
            address={address}
            backgroundColor={backgroundColor}
            fontColor={fontColor}
            profileName={profileName}
            profileBio={profileBio}
            profileShape={profileShape}
            profileNameStyle={profileNameStyle}
            profileHorizontalPosition={profileHorizontalPosition}
            profileNameHorizontalPosition={profileNameHorizontalPosition}
          />
        );

      case 'hero':
        return (
          <RenderHero
            key={`${componentType}-${component.order}`}
            address={address}
            backgroundColor={backgroundColor}
            fontColor={fontColor}
            heroContent={heroContent}
          />
        );

      case 'sociallinks':
        return (
          <RenderSocialLinks
            key={`${componentType}-${component.order}`}
            address={address}
            backgroundColor={backgroundColor}
            fontColor={fontColor}
            socialLinks={socialLinks}
            referralCode={details?.referralCode}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-black">
      {/* Profile Header - Always show basic info */}
      <div className="bg-gradient-to-r from-blue-900 to-purple-900 text-white p-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold">
            {name || `Profile ${address.substring(0, 8)}...`}
          </h1>
          <p className="text-blue-200 text-sm">
            {chain} • {address.substring(0, 10)}...{address.substring(address.length - 8)}
          </p>
        </div>
      </div>

      {/* Render Components */}
      <div className="w-full">
        {sortedComponents.map((component) => renderComponent(component))}
      </div>

      {/* Footer */}
      <div className="bg-neutral-900 text-neutral-400 py-8 px-6 text-center">
        <div className="max-w-4xl mx-auto">
          <p className="text-sm">
            Powered by{" "}
            <a 
              href={process.env.NEXT_PUBLIC_MAIN_SITE_URL || "https://web3socials.fun"}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-400 hover:text-blue-300 transition-colors"
            >
              Web3Socials
            </a>
          </p>
          <p className="text-xs mt-2 opacity-75">
            Create your own Web3 profile and showcase it anywhere
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProfileViewer;
