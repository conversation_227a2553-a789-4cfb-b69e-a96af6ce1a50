#!/bin/bash

# Web3Socials Profile Viewer Deployment Script
# This script helps deploy the profile viewer to various platforms

set -e

echo "🚀 Web3Socials Profile Viewer Deployment"
echo "========================================"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found!"
    echo "Please copy .env.example to .env and configure your database connection."
    exit 1
fi

# Function to deploy to Vercel
deploy_vercel() {
    echo "📦 Deploying to Vercel..."
    
    if ! command -v vercel &> /dev/null; then
        echo "Installing Vercel CLI..."
        npm install -g vercel
    fi
    
    echo "Building and deploying..."
    vercel --prod
    
    echo "✅ Deployed to Vercel successfully!"
}

# Function to build Docker image
build_docker() {
    echo "🐳 Building Docker image..."
    
    # Get project name from package.json
    PROJECT_NAME=$(node -p "require('./package.json').name")
    
    echo "Building image: $PROJECT_NAME"
    docker build -t $PROJECT_NAME .
    
    echo "✅ Docker image built successfully!"
    echo "To run the container:"
    echo "docker run -p 3000:3000 --env-file .env $PROJECT_NAME"
}

# Function to test database connection
test_database() {
    echo "🔍 Testing database connection..."
    npm run test:db
}

# Function to build for production
build_production() {
    echo "🏗️  Building for production..."
    npm run build
    echo "✅ Production build completed!"
}

# Main menu
echo ""
echo "Select deployment option:"
echo "1) Test database connection"
echo "2) Build for production"
echo "3) Build Docker image"
echo "4) Deploy to Vercel"
echo "5) Exit"
echo ""

read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        test_database
        ;;
    2)
        build_production
        ;;
    3)
        build_docker
        ;;
    4)
        deploy_vercel
        ;;
    5)
        echo "👋 Goodbye!"
        exit 0
        ;;
    *)
        echo "❌ Invalid option. Please choose 1-5."
        exit 1
        ;;
esac

echo ""
echo "🎉 Operation completed successfully!"
