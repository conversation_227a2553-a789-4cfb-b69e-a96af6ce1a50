import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { profileReferrals } from '@/db/schema';
import { eq } from 'drizzle-orm';

interface Context {
  params: Promise<{ code: string }>;
}

export async function GET(
  _request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { code } = await context.params;

    if (!code) {
      return Response.json(
        { error: 'Referral code is required' },
        { status: 400 }
      );
    }

    // Count referrals for the specified code
    const referrals = await db
      .select()
      .from(profileReferrals)
      .where(eq(profileReferrals.referralCode, code));

    return Response.json({ count: referrals.length });
  } catch (error: any) {
    console.error('Failed to fetch referral count:', error);

    if (error.code === 'ECONNREFUSED') {
      return Response.json(
        { error: 'Database is currently unavailable. Please try again later.' },
        { status: 503 }
      );
    }

    return Response.json(
      { error: 'Failed to fetch referral count' },
      { status: 500 }
    );
  }
}
