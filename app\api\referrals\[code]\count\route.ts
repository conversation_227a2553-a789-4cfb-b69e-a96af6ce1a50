import { NextRequest } from 'next/server';

interface Context {
  params: Promise<{ code: string }>;
}

export async function GET(
  _request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { code } = await context.params;

    if (!code) {
      return Response.json(
        { error: 'Referral code is required' },
        { status: 400 }
      );
    }

    // During build time, return zero count
    if (process.env.NEXT_PHASE === 'phase-production-build') {
      return Response.json({ count: 0 });
    }

    // Import database modules only when needed
    const { db } = await import('@/db/drizzle');
    const { profileReferrals } = await import('@/db/schema');
    const { eq } = await import('drizzle-orm');

    // Count referrals for the specified code
    const referrals = await db
      .select()
      .from(profileReferrals)
      .where(eq(profileReferrals.referralCode, code));

    return Response.json({ count: referrals.length });
  } catch (error: any) {
    console.error('Failed to fetch referral count:', error);

    if (error.code === 'ECONNREFUSED') {
      return Response.json(
        { error: 'Database is currently unavailable. Please try again later.' },
        { status: 503 }
      );
    }

    return Response.json(
      { error: 'Failed to fetch referral count' },
      { status: 500 }
    );
  }
}
