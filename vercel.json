{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"DATABASE_URL": "@database_url", "MYSQL_SSL": "@mysql_ssl", "NEXT_PUBLIC_MAIN_SITE_URL": "@main_site_url", "NEXT_PUBLIC_SITE_URL": "@site_url"}, "functions": {"app/api/profile/[name]/route.ts": {"maxDuration": 10}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "rewrites": [{"source": "/api/:path*", "destination": "/api/:path*"}]}