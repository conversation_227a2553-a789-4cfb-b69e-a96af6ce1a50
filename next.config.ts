import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'unsafe-none',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN', // Allow embedding in iframes from same origin
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ];
  },
  // Enable static optimization for better performance
  output: 'standalone',
  // Optimize images
  images: {
    domains: ['localhost'],
    unoptimized: true, // For base64 images
  },
  // Don't try to prerender API routes
  trailingSlash: false,
  skipTrailingSlashRedirect: true,
};

export default nextConfig;
