# Web3Socials Profile Viewer

A lightweight profile viewer for Web3Socials that can be deployed on external domains to display user profiles via `/name` routes.

## Features

- 🔗 **External Domain Support**: Deploy on any domain to display Web3Socials profiles
- 📱 **Responsive Design**: Mobile-friendly profile viewing
- 🚀 **Lightweight**: Minimal dependencies, fast loading
- 🔒 **Read-Only**: No authentication required, secure profile viewing
- 🎨 **Consistent Rendering**: Uses the same components as the main Web3Socials app
- 🌐 **SEO Friendly**: Optimized for search engines and social sharing
- ✨ **Text Effects**: Supports typewriter and decrypted text animations
- 🖼️ **Image Support**: Displays banner, profile, and hero images
- 🔗 **Social Links**: Interactive floating dock for social media links
- 📊 **Referral Tracking**: Shows referral codes and usage counts

## Quick Start

1. **Clone and Install**

   ```bash
   cd web3socials-viewer
   npm install
   ```

2. **Configure Environment**

   ```bash
   cp .env.example .env
   # Edit .env with your database connection details
   ```

3. **Test Database Connection**

   ```bash
   npm run test:db
   ```

4. **Run Development Server**

   ```bash
   npm run dev
   ```

5. **Build for Production**
   ```bash
   npm run build
   npm start
   ```

## Usage

Once deployed, profiles can be accessed via:

- `https://yourdomain.com/username` - Display profile by name
- `https://yourdomain.com/0x...` - Display profile by wallet address

## Configuration

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```bash
# Database Configuration (same as your main Web3Socials app)
DATABASE_URL=mysql://username:password@localhost:3306/web3profiles
MYSQL_SSL=false

# Application Configuration
NODE_ENV=production
NEXT_PUBLIC_APP_NAME=Web3Socials Viewer
NEXT_PUBLIC_MAIN_SITE_URL=https://web3socials.fun
NEXT_PUBLIC_SITE_URL=https://your-viewer-domain.com

# Optional: Analytics
# NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id
```

### Database

This viewer uses the same database as your main Web3Socials application. It requires read access to the following tables:

- `web3Profile`
- `componentPositions`
- `componentImages`
- `profileReferrals`

No additional database setup is required.

## Deployment

### Vercel (Recommended)

1. **Connect Repository**

   ```bash
   # Push your code to GitHub/GitLab
   git init
   git add .
   git commit -m "Initial commit"
   git remote add origin your-repo-url
   git push -u origin main
   ```

2. **Deploy to Vercel**

   - Connect your repository to Vercel
   - Set environment variables in Vercel dashboard:
     - `DATABASE_URL`
     - `MYSQL_SSL`
     - `NEXT_PUBLIC_MAIN_SITE_URL`
     - `NEXT_PUBLIC_SITE_URL`
   - Deploy automatically

3. **Using Deploy Script**
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   # Select option 4 for Vercel deployment
   ```

### Docker

```bash
# Build Docker image
docker build -t web3socials-viewer .

# Run container
docker run -p 3000:3000 --env-file .env web3socials-viewer

# Or use deploy script
./deploy.sh
# Select option 3 for Docker build
```

### Traditional Server

```bash
# Build the application
npm run build

# Start with PM2
pm2 start npm --name "web3socials-viewer" -- start

# Or start directly
npm start
```

## API Routes

- `GET /api/profile/[name]` - Fetch profile data by name or address
- `GET /api/images/[address]/[componentType]` - Fetch images for a component
- `GET /api/referrals/[code]/count` - Get referral usage count

## Troubleshooting

### Database Connection Issues

1. **Test your connection:**

   ```bash
   npm run test:db
   ```

2. **Common issues:**

   - Incorrect DATABASE_URL format
   - Database server not accessible
   - Wrong credentials
   - SSL configuration mismatch

3. **SSL Issues:**
   - Set `MYSQL_SSL=true` for cloud databases
   - Set `MYSQL_SSL=false` for local databases

### Profile Not Found

- Ensure the profile exists in your main Web3Socials database
- Check that the profile status is 'approved'
- Verify the name/address is correct
- Check database connectivity

## License

MIT License - See main Web3Socials project for details.
